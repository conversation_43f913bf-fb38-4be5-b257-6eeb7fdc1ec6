<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>8D报告协作系统</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/d8_form.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/conversation_styles.css') }}">
    <script src="{{ url_for('static', filename='js/conversation_manager.js') }}"></script>
    <script src="{{ url_for('static', filename='js/conversation_ui.js') }}"></script>
    <script src="{{ url_for('static', filename='js/d8_form.js') }}"></script>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-content">
                <div class="header-left">
                    <h1 id="conversation-title">8D报告协作系统</h1>
                    <p id="conversation-subtitle">智能协作，优化报告质量</p>
                </div>

            </div>
        </div>
        
        <div class="form-container">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }}">
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            <!-- 优化后的统一侧边栏 -->
            <div class="unified-sidebar" id="unified-sidebar">
                <!-- 侧边栏头部 -->
                <div class="sidebar-header">
                    <div class="user-info">
                        <div class="user-avatar">👤</div>
                        <div class="user-name">豆包</div>
                    </div>
                    <button class="sidebar-toggle-btn" id="sidebar-toggle-btn" onclick="toggleSidebar()">
                        <span class="toggle-icon">📋</span>
                    </button>
                </div>

                <!-- 新建对话按钮 -->
                <div class="new-conversation-section">
                    <button class="new-conversation-btn" onclick="createNewConversation()">
                        <span class="btn-icon">+</span>
                        <span class="btn-text">新对话</span>
                        <span class="btn-shortcut">Ctrl+N</span>
                    </button>
                </div>

                <!-- 功能导航区域 -->
                <div class="sidebar-navigation">
                    <div class="nav-item" onclick="navigateToAISearch()">
                        <span class="nav-icon">🔍</span>
                        <span class="nav-text">AI 搜索</span>
                    </div>
                    <div class="nav-item" onclick="navigateToHelp()">
                        <span class="nav-icon">✏️</span>
                        <span class="nav-text">帮我写作</span>
                    </div>
                    <div class="nav-item" onclick="navigateToCode()">
                        <span class="nav-icon">💻</span>
                        <span class="nav-text">AI 编程</span>
                    </div>
                    <div class="nav-item" onclick="navigateToImage()">
                        <span class="nav-icon">🎨</span>
                        <span class="nav-text">图像生成</span>
                    </div>
                    <div class="nav-item nav-more" onclick="toggleMoreOptions()">
                        <span class="nav-icon">📋</span>
                        <span class="nav-text">更多</span>
                        <span class="nav-arrow">›</span>
                    </div>
                </div>

                <!-- 对话历史列表 -->
                <div class="conversation-history-section">
                    <div class="section-title">历史对话</div>
                    <div class="conversation-list" id="conversation-list">
                        <div class="no-conversations" id="no-conversations">
                            暂无对话记录
                        </div>
                    </div>
                </div>

                <!-- 当前对话的版本列表 -->
                <div class="version-history-section" id="version-history-section" style="display: none;">
                    <div class="section-title">版本记录</div>
                    <div class="version-list" id="version-list">
                        <!-- 版本项将通过JavaScript动态添加 -->
                    </div>
                </div>
            </div>

            <!-- 侧边栏遮罩层（移动端） -->
            <div class="sidebar-overlay" id="sidebar-overlay" onclick="closeSidebar()"></div>

            <!-- 悬浮的侧边栏切换按钮（中等屏幕显示） -->
            <button class="floating-sidebar-toggle" id="floating-sidebar-toggle" onclick="toggleSidebar()" style="display: none;">
                📋
            </button>

            <!-- 固定悬浮导航 -->
            <div class="fixed-navigation">
                <div class="nav-container">
                    <div class="nav-top">
                        <div class="nav-items">
                            <a href="javascript:void(0)" class="nav-item active" data-section="guide">
                                <span class="nav-number">📝</span>
                                <span class="nav-title">修改意见</span>
                                <span class="nav-progress-mini" id="nav-progress-guide">-</span>
                            </a>
                            <a href="javascript:void(0)" class="nav-item" data-section="d0">
                                <span class="nav-number">D0</span>
                                <span class="nav-title">基本信息</span>
                                <span class="nav-progress-mini" id="nav-progress-d0">0/4</span>
                            </a>
                            <a href="javascript:void(0)" class="nav-item" data-section="d1">
                                <span class="nav-number">D1</span>
                                <span class="nav-title">建立小组</span>
                                <span class="nav-progress-mini" id="nav-progress-d1">0/0</span>
                            </a>
                            <a href="javascript:void(0)" class="nav-item" data-section="d2">
                                <span class="nav-number">D2</span>
                                <span class="nav-title">问题描述</span>
                                <span class="nav-progress-mini" id="nav-progress-d2">0/8</span>
                            </a>
                            <a href="javascript:void(0)" class="nav-item" data-section="d3">
                                <span class="nav-number">D3</span>
                                <span class="nav-title">临时措施</span>
                                <span class="nav-progress-mini" id="nav-progress-d3">0/0</span>
                            </a>
                            <a href="javascript:void(0)" class="nav-item" data-section="d4">
                                <span class="nav-number">D4</span>
                                <span class="nav-title">根本原因</span>
                                <span class="nav-progress-mini" id="nav-progress-d4">0/0</span>
                            </a>
                            <a href="javascript:void(0)" class="nav-item" data-section="d5">
                                <span class="nav-number">D5</span>
                                <span class="nav-title">永久措施</span>
                                <span class="nav-progress-mini" id="nav-progress-d5">0/0</span>
                            </a>
                            <a href="javascript:void(0)" class="nav-item" data-section="d6">
                                <span class="nav-number">D6</span>
                                <span class="nav-title">措施验证</span>
                                <span class="nav-progress-mini" id="nav-progress-d6">0/0</span>
                            </a>
                            <a href="javascript:void(0)" class="nav-item" data-section="d7">
                                <span class="nav-number">D7</span>
                                <span class="nav-title">预防措施</span>
                                <span class="nav-progress-mini" id="nav-progress-d7">0/0</span>
                            </a>
                            <a href="javascript:void(0)" class="nav-item" data-section="d8">
                                <span class="nav-number">D8</span>
                                <span class="nav-title">庆贺团队</span>
                                <span class="nav-progress-mini" id="nav-progress-d8">0/0</span>
                            </a>
                        </div>

                    </div>

                </div>
            </div>

            <!-- 表单主体区域 -->
            <div class="form-main-container">
                <!-- 表单内容 -->
                <div class="form-content">
            <form id="d8-form" method="POST" action="/submit_8d_report">
                <!-- 修改意见 -->
                <div class="form-section" id="section-guide" data-section="guide">
                    <div class="section-header" onclick="toggleSection('guide')">
                        <div class="section-title-container">
                            <h2 class="section-title">📝 修改意见</h2>
                            <div class="section-desc">显示此版本的修改意见和要求。</div>
                        </div>
                        <div class="section-controls">
                            <span class="completion-badge" id="completion-guide">意见</span>
                            <button type="button" class="collapse-btn" id="collapse-btn-guide">
                                <span class="collapse-icon">−</span>
                            </button>
                        </div>
                    </div>
                    <div class="section-content">
                        <div class="guide-content" id="modification-notes-content">
                            <div id="modification-notes-display">
                                <!-- 修改意见内容将通过JavaScript动态填充 -->
                                <div class="no-modifications">
                                    <p>这是初始版本，暂无修改意见。</p>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
                
                <!-- D0 基本信息 -->
                <div class="form-section" id="section-d0" data-section="d0">
                    <div class="section-header" onclick="toggleSection('d0')">
                        <div class="section-title-container">
                            <h2 class="section-title">D0 基本信息</h2>
                            <div class="section-desc">请填写标题、汇报人、汇报时间以及项目背景等。</div>
                        </div>
                        <div class="section-controls">
                            <span class="completion-badge" id="completion-d0">0/4</span>
                            <button type="button" class="collapse-btn" id="collapse-btn-d0">
                                <span class="collapse-icon">−</span>
                            </button>
                        </div>
                    </div>
                    <div class="section-content">
                        <div class="form-row-d0">
                            <div class="form-group">
                                <label for="d0_title">标题</label>
                                <textarea id="d0_title" name="d0_title"></textarea>
                            </div>
                            <div class="form-group">
                                <label for="d0_reporter">汇报人</label>
                                <textarea id="d0_reporter" name="d0_reporter"></textarea>
                            </div>
                            <div class="form-group">
                                <label for="d0_time">汇报时间</label>
                                <input type="date" id="d0_time" name="d0_time">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="d0_background">项目背景 <span class="required">*</span></label>
                            <textarea id="d0_background" name="d0_background" required></textarea>
                        </div>
                    </div>
                </div>
                
                <!-- D1 建立小组 -->
                <div class="form-section" id="section-d1" data-section="d1">
                    <div class="section-header" onclick="toggleSection('d1')">
                        <div class="section-title-container">
                            <h2 class="section-title">D1 建立小组</h2>
                            <div class="section-desc">填写小组成员信息，3至10人，要求是由来自多个部门的具有一定职权和技能的人员组成的跨部门小组。</div>
                        </div>
                        <div class="section-controls">
                            <span class="completion-badge" id="completion-d1">0/0</span>
                            <button type="button" class="collapse-btn" id="collapse-btn-d1">
                                <span class="collapse-icon">−</span>
                            </button>
                        </div>
                    </div>
                    <div class="section-content">
                        <!-- 组长 -->
                        <div class="member-group">
                            <div class="group-title">组长</div>
                            <div class="form-row-4">
                                <div class="form-group">
                                    <label for="d1_leader_name">姓名</label>
                                    <textarea id="d1_leader_name" name="d1_leader_name"></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="d1_leader_dept">部门</label>
                                    <textarea id="d1_leader_dept" name="d1_leader_dept"></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="d1_leader_position">职位</label>
                                    <textarea id="d1_leader_position" name="d1_leader_position"></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="d1_leader_responsibility">主要职责</label>
                                    <textarea id="d1_leader_responsibility" name="d1_leader_responsibility" class="auto-resize-textarea"></textarea>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 固定成员1-2 -->
                        <div class="member-group">
                            <div class="group-title">成员1</div>
                            <div class="form-row-4">
                                <div class="form-group">
                                    <label for="d1_member1_name">姓名</label>
                                    <textarea id="d1_member1_name" name="d1_member1_name"></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="d1_member1_dept">部门</label>
                                    <textarea id="d1_member1_dept" name="d1_member1_dept"></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="d1_member1_position">职位</label>
                                    <textarea id="d1_member1_position" name="d1_member1_position"></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="d1_member1_responsibility">主要职责</label>
                                    <textarea id="d1_member1_responsibility" name="d1_member1_responsibility" class="auto-resize-textarea"></textarea>
                                </div>
                            </div>
                        </div>
                        
                        <div class="member-group">
                            <div class="group-title">成员2</div>
                            <div class="form-row-4">
                                <div class="form-group">
                                    <label for="d1_member2_name">姓名</label>
                                    <textarea id="d1_member2_name" name="d1_member2_name"></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="d1_member2_dept">部门</label>
                                    <textarea id="d1_member2_dept" name="d1_member2_dept"></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="d1_member2_position">职位</label>
                                    <textarea id="d1_member2_position" name="d1_member2_position"></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="d1_member2_responsibility">主要职责</label>
                                    <textarea id="d1_member2_responsibility" name="d1_member2_responsibility" class="auto-resize-textarea"></textarea>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 动态添加的成员容器 -->
                        <div id="dynamic-members-container"></div>
                        
                        <!-- 添加成员按钮 -->
                        <div class="add-member-section">
                            <button type="button" class="add-member-btn" onclick="addMember()">
                                <span>增加成员</span>
                            </button>
                            <div class="member-counter">
                                当前成员数：<span id="member-count">3</span> 人 (含组长)
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- D2 问题描述 -->
                <div class="form-section" id="section-d2" data-section="d2">
                    <div class="section-header" onclick="toggleSection('d2')">
                        <div class="section-title-container">
                            <h2 class="section-title">D2 问题描述</h2>
                            <div class="section-desc">先进行整体描述，再按5W2H详细描述。</div>
                        </div>
                        <div class="section-controls">
                            <span class="completion-badge" id="completion-d2">0/8</span>
                            <button type="button" class="collapse-btn" id="collapse-btn-d2">
                                <span class="collapse-icon">−</span>
                            </button>
                        </div>
                    </div>
                    <div class="section-content">
                        <div class="form-group">
                            <label for="d2_description">事件整体描述 <span class="required">*</span></label>
                            <textarea id="d2_description" name="d2_description" class="auto-resize-textarea" required></textarea>
                        </div>
                        
                        <!-- 5W2H分析组 -->
                        <div class="analysis-group">
                            <div class="group-title">5W2H分析</div>
                            <!-- 前三个在一行：何时、何地、何人 -->
                            <div class="form-row-3">
                                <div class="form-group">
                                    <label for="d2_when">何时发生 <span class="required">*</span></label>
                                    <textarea id="d2_when" name="d2_when" class="auto-resize-textarea" required></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="d2_where">何地发生 <span class="required">*</span></label>
                                    <textarea id="d2_where" name="d2_where" class="auto-resize-textarea" required></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="d2_who">何人发现 <span class="required">*</span></label>
                                    <textarea id="d2_who" name="d2_who" required></textarea>
                                </div>
                            </div>
                            <!-- 后两个在一行：什么问题、为什么 -->
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="d2_what">发生了什么问题 <span class="required">*</span></label>
                                    <textarea id="d2_what" name="d2_what" class="auto-resize-textarea" required></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="d2_why">为什么发生这问题 <span class="required">*</span></label>
                                    <textarea id="d2_why" name="d2_why" class="auto-resize-textarea" required></textarea>
                                </div>
                            </div>
                            <!-- 最后两个在一行：如何发生、影响程度 -->
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="d2_how">问题如何发生 <span class="required">*</span></label>
                                    <textarea id="d2_how" name="d2_how" class="auto-resize-textarea" required></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="d2_impact">问题数量和影响程度 <span class="required">*</span></label>
                                    <textarea id="d2_impact" name="d2_impact" class="auto-resize-textarea" required></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- D3 临时措施 -->
                <div class="form-section" id="section-d3" data-section="d3">
                    <div class="section-header" onclick="toggleSection('d3')">
                        <div class="section-title-container">
                            <h2 class="section-title">D3 临时措施</h2>
                            <div class="section-desc">填写消除问题影响的临时措施及责任人、完成期限等。</div>
                        </div>
                        <div class="section-controls">
                            <span class="completion-badge" id="completion-d3">0/0</span>
                            <button type="button" class="collapse-btn" id="collapse-btn-d3">
                                <span class="collapse-icon">−</span>
                            </button>
                        </div>
                    </div>
                    <div class="section-content">
                        <div class="measure-group">
                            <div class="group-title">临时措施1</div>
                            <!-- 前两个字段：范围和处置对策，处置对策更宽 -->
                            <div class="form-row-d3-top">
                                <div class="form-group">
                                    <label for="d3_scope1">范围</label>
                                    <textarea id="d3_scope1" name="d3_scope1" class="auto-resize-textarea"></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="d3_measure1">处置对策</label>
                                    <textarea id="d3_measure1" name="d3_measure1" class="auto-resize-textarea"></textarea>
                                </div>
                            </div>
                            <!-- 后四个字段：责任人、完成期限、状态、进度备注 -->
                            <div class="form-row-4">
                                <div class="form-group">
                                    <label for="d3_responsible1">责任人</label>
                                    <textarea id="d3_responsible1" name="d3_responsible1"></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="d3_deadline1">完成期限</label>
                                    <input type="date" id="d3_deadline1" name="d3_deadline1">
                                </div>
                                <div class="form-group">
                                    <label for="d3_status1">状态</label>
                                    <select id="d3_status1" name="d3_status1">
                                        <option value="">请选择</option>
                                        <option value="已完成">已完成</option>
                                        <option value="进行中">进行中</option>
                                        <option value="未开始">未开始</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="d3_note1">进度备注</label>
                                    <textarea id="d3_note1" name="d3_note1"></textarea>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 动态添加的措施容器 -->
                        <div id="dynamic-measures-container"></div>
                        
                        <!-- 添加措施按钮 -->
                        <div class="add-member-section">
                            <button type="button" class="add-member-btn" onclick="addMeasure()">
                                <span>增加措施</span>
                            </button>
                            <div class="member-counter">
                                当前措施数：<span id="measure-count">1</span> 个
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- D4 根本原因 -->
                <div class="form-section" id="section-d4" data-section="d4">
                    <div class="section-header" onclick="toggleSection('d4')">
                        <div class="section-title-container">
                            <h2 class="section-title">D4 根本原因</h2>
                            <div class="section-desc">用5Why、人机料法环测等分析并确认根本原因。</div>
                        </div>
                        <div class="section-controls">
                            <span class="completion-badge" id="completion-d4">0/0</span>
                            <button type="button" class="collapse-btn" id="collapse-btn-d4">
                                <span class="collapse-icon">−</span>
                            </button>
                        </div>
                    </div>
                    <div class="section-content">
                        <!-- 5Why分析组 -->
                        <div class="analysis-group">
                            <div class="group-title">5Why分析</div>
                            {% for i in range(1, 6) %}
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="d4_why{{ i }}">Why{{ i }}</label>
                                    <textarea id="d4_why{{ i }}" name="d4_why{{ i }}" class="auto-resize-textarea"></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="d4_answer{{ i }}">Answer{{ i }}</label>
                                    <textarea id="d4_answer{{ i }}" name="d4_answer{{ i }}" class="auto-resize-textarea"></textarea>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        
                        <!-- "人"可能原因组 -->
                        <div class="analysis-group">
                            <div class="group-title">
                                "人"可能原因
                                <div class="cause-actions">
                                    <button type="button" class="add-cause-btn" onclick="addCause('man', '&quot;人&quot;原因')">增加</button>
                                </div>
                            </div>
                            <div id="man-causes-container">
                                <div class="form-row-d4-analysis" id="man-cause-1">
                                    <div class="form-group">
                                        <label for="d4_man1">"人"原因1</label>
                                        <textarea id="d4_man1" name="d4_man1" class="auto-resize-textarea"></textarea>
                                    </div>
                                    <div class="form-group">
                                        <label for="d4_man1_judgment">判定</label>
                                        <select id="d4_man1_judgment" name="d4_man1_judgment">
                                            <option value="">请选择</option>
                                            <option value="主因">主因</option>
                                            <option value="次因">次因</option>
                                            <option value="排除">排除</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="d4_man1_evidence">证据</label>
                                        <textarea id="d4_man1_evidence" name="d4_man1_evidence" class="auto-resize-textarea"></textarea>
                                    </div>
                                    <div class="form-group inline-actions">
                                        <!-- 人原因1不可删除，确保至少保留一个 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- "机"可能原因组 -->
                        <div class="analysis-group">
                            <div class="group-title">
                                "机"可能原因
                                <div class="cause-actions">
                                    <button type="button" class="add-cause-btn" onclick="addCause('machine', '&quot;机&quot;原因')">增加</button>
                                </div>
                            </div>
                            <div id="machine-causes-container">
                                <div class="form-row-d4-analysis" id="machine-cause-1">
                                    <div class="form-group">
                                        <label for="d4_machine1">"机"原因1</label>
                                        <textarea id="d4_machine1" name="d4_machine1" class="auto-resize-textarea"></textarea>
                                    </div>
                                    <div class="form-group">
                                        <label for="d4_machine1_judgment">判定</label>
                                        <select id="d4_machine1_judgment" name="d4_machine1_judgment">
                                            <option value="">请选择</option>
                                            <option value="主因">主因</option>
                                            <option value="次因">次因</option>
                                            <option value="排除">排除</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="d4_machine1_evidence">证据</label>
                                        <textarea id="d4_machine1_evidence" name="d4_machine1_evidence" class="auto-resize-textarea"></textarea>
                                    </div>
                                    <div class="form-group inline-actions">
                                        <!-- 机原因1不可删除，确保至少保留一个 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- "料"可能原因组 -->
                        <div class="analysis-group">
                            <div class="group-title">
                                "料"可能原因
                                <div class="cause-actions">
                                    <button type="button" class="add-cause-btn" onclick="addCause('material', '&quot;料&quot;原因')">增加</button>
                                </div>
                            </div>
                            <div id="material-causes-container">
                                <div class="form-row-d4-analysis" id="material-cause-1">
                                    <div class="form-group">
                                        <label for="d4_material1">"料"原因1</label>
                                        <textarea id="d4_material1" name="d4_material1" class="auto-resize-textarea"></textarea>
                                    </div>
                                    <div class="form-group">
                                        <label for="d4_material1_judgment">判定</label>
                                        <select id="d4_material1_judgment" name="d4_material1_judgment">
                                            <option value="">请选择</option>
                                            <option value="主因">主因</option>
                                            <option value="次因">次因</option>
                                            <option value="排除">排除</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="d4_material1_evidence">证据</label>
                                        <textarea id="d4_material1_evidence" name="d4_material1_evidence" class="auto-resize-textarea"></textarea>
                                    </div>
                                    <div class="form-group inline-actions">
                                        <!-- 料原因1不可删除，确保至少保留一个 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- "法"可能原因组 -->
                        <div class="analysis-group">
                            <div class="group-title">
                                "法"可能原因
                                <div class="cause-actions">
                                    <button type="button" class="add-cause-btn" onclick="addCause('method', '&quot;法&quot;原因')">增加</button>
                                </div>
                            </div>
                            <div id="method-causes-container">
                                <div class="form-row-d4-analysis" id="method-cause-1">
                                    <div class="form-group">
                                        <label for="d4_method1">"法"原因1</label>
                                        <textarea id="d4_method1" name="d4_method1" class="auto-resize-textarea"></textarea>
                                    </div>
                                    <div class="form-group">
                                        <label for="d4_method1_judgment">判定</label>
                                        <select id="d4_method1_judgment" name="d4_method1_judgment">
                                            <option value="">请选择</option>
                                            <option value="主因">主因</option>
                                            <option value="次因">次因</option>
                                            <option value="排除">排除</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="d4_method1_evidence">证据</label>
                                        <textarea id="d4_method1_evidence" name="d4_method1_evidence" class="auto-resize-textarea"></textarea>
                                    </div>
                                    <div class="form-group inline-actions">
                                        <!-- 法原因1不可删除，确保至少保留一个 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- "环"可能原因组 -->
                        <div class="analysis-group">
                            <div class="group-title">
                                "环"可能原因
                                <div class="cause-actions">
                                    <button type="button" class="add-cause-btn" onclick="addCause('environment', '&quot;环&quot;原因')">增加</button>
                                </div>
                            </div>
                            <div id="environment-causes-container">
                                <div class="form-row-d4-analysis" id="environment-cause-1">
                                    <div class="form-group">
                                        <label for="d4_environment1">"环"原因1</label>
                                        <textarea id="d4_environment1" name="d4_environment1" class="auto-resize-textarea"></textarea>
                                    </div>
                                    <div class="form-group">
                                        <label for="d4_environment1_judgment">判定</label>
                                        <select id="d4_environment1_judgment" name="d4_environment1_judgment">
                                            <option value="">请选择</option>
                                            <option value="主因">主因</option>
                                            <option value="次因">次因</option>
                                            <option value="排除">排除</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="d4_environment1_evidence">证据</label>
                                        <textarea id="d4_environment1_evidence" name="d4_environment1_evidence" class="auto-resize-textarea"></textarea>
                                    </div>
                                    <div class="form-group inline-actions">
                                        <!-- 环原因1不可删除，确保至少保留一个 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- "测"可能原因组 -->
                        <div class="analysis-group">
                            <div class="group-title">
                                "测"可能原因
                                <div class="cause-actions">
                                    <button type="button" class="add-cause-btn" onclick="addCause('measurement', '&quot;测&quot;原因')">增加</button>
                                </div>
                            </div>
                            <div id="measurement-causes-container">
                                <div class="form-row-d4-analysis" id="measurement-cause-1">
                                    <div class="form-group">
                                        <label for="d4_measurement1">"测"原因1</label>
                                        <textarea id="d4_measurement1" name="d4_measurement1" class="auto-resize-textarea"></textarea>
                                    </div>
                                    <div class="form-group">
                                        <label for="d4_measurement1_judgment">判定</label>
                                        <select id="d4_measurement1_judgment" name="d4_measurement1_judgment">
                                            <option value="">请选择</option>
                                            <option value="主因">主因</option>
                                            <option value="次因">次因</option>
                                            <option value="排除">排除</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="d4_measurement1_evidence">证据</label>
                                        <textarea id="d4_measurement1_evidence" name="d4_measurement1_evidence" class="auto-resize-textarea"></textarea>
                                    </div>
                                    <div class="form-group inline-actions">
                                        <!-- 测原因1不可删除，确保至少保留一个 -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        
                        <div class="form-group">
                            <label for="d4_summary">原因小结</label>
                            <textarea id="d4_summary" name="d4_summary" class="auto-resize-textarea"></textarea>
                        </div>
                    </div>
                </div>
                
                <!-- D5 永久措施 -->
                <div class="form-section" id="section-d5" data-section="d5">
                    <div class="section-header" onclick="toggleSection('d5')">
                        <div class="section-title-container">
                            <h2 class="section-title">D5 永久措施</h2>
                            <div class="section-desc">针对根本原因制定具体、可操作的永久措施。</div>
                        </div>
                        <div class="section-controls">
                            <span class="completion-badge" id="completion-d5">0/0</span>
                            <button type="button" class="collapse-btn" id="collapse-btn-d5">
                                <span class="collapse-icon">−</span>
                            </button>
                        </div>
                    </div>
                    <div class="section-content">
                        <!-- 固定措施1 -->
                        <div class="measure-group" id="d5-measure-1">
                            <div class="group-title">措施1</div>
                            <div class="form-row-d5">
                                <div class="form-group">
                                    <label for="d5_measure1">纠正措施</label>
                                    <textarea id="d5_measure1" name="d5_measure1" class="auto-resize-textarea"></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="d5_responsible1">责任人</label>
                                    <textarea id="d5_responsible1" name="d5_responsible1"></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="d5_deadline1">计划完成日期</label>
                                    <input type="date" id="d5_deadline1" name="d5_deadline1">
                                </div>
                            </div>
                        </div>
                        
                        <!-- 动态措施容器 -->
                        <div id="dynamic-d5-measures-container"></div>
                        
                        <!-- 增加措施按钮 -->
                        <div class="add-member-section">
                            <button type="button" class="add-member-btn" onclick="addD5Measure()">
                                <span>增加措施</span>
                            </button>
                            <div class="member-counter">
                                当前措施数：<span id="d5-measure-count">1</span> 个
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- D6 措施验证 -->
                <div class="form-section" id="section-d6" data-section="d6">
                    <div class="section-header" onclick="toggleSection('d6')">
                        <div class="section-title-container">
                            <h2 class="section-title">D6 措施验证</h2>
                            <div class="section-desc">填写对永久措施的验证方法、验证人及验证结果。</div>
                        </div>
                        <div class="section-controls">
                            <span class="completion-badge" id="completion-d6">0/0</span>
                            <button type="button" class="collapse-btn" id="collapse-btn-d6">
                                <span class="collapse-icon">−</span>
                            </button>
                        </div>
                    </div>
                    <div class="section-content">
                        <!-- 固定验证1 -->
                        <div class="verification-group" id="d6-verification-1">
                            <div class="group-title">验证1</div>
                            <div class="form-row-d6">
                                <div class="form-group">
                                    <label for="d6_verification1">措施验证</label>
                                    <textarea id="d6_verification1" name="d6_verification1" class="auto-resize-textarea"></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="d6_verifier1">验证人</label>
                                    <textarea id="d6_verifier1" name="d6_verifier1"></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="d6_time1">验证时间</label>
                                    <input type="date" id="d6_time1" name="d6_time1">
                                </div>
                                <div class="form-group">
                                    <label for="d6_result1">验证结果</label>
                                    <textarea id="d6_result1" name="d6_result1" class="auto-resize-textarea"></textarea>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 动态验证容器 -->
                        <div id="dynamic-d6-verifications-container"></div>
                        
                        <!-- 增加验证按钮 -->
                        <div class="add-member-section">
                            <button type="button" class="add-member-btn" onclick="addD6Verification()">
                                <span>增加验证</span>
                            </button>
                            <div class="member-counter">
                                当前验证数：<span id="d6-verification-count">1</span> 个
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- D7 预防措施 -->
                <div class="form-section" id="section-d7" data-section="d7">
                    <div class="section-header" onclick="toggleSection('d7')">
                        <div class="section-title-container">
                            <h2 class="section-title">D7 预防措施</h2>
                            <div class="section-desc">制定防止类似问题再次发生的标准化、管理性措施。</div>
                        </div>
                        <div class="section-controls">
                            <span class="completion-badge" id="completion-d7">0/0</span>
                            <button type="button" class="collapse-btn" id="collapse-btn-d7">
                                <span class="collapse-icon">−</span>
                            </button>
                        </div>
                    </div>
                    <div class="section-content">
                        <!-- 固定预防1 -->
                        <div class="prevention-group" id="d7-prevention-1">
                            <div class="group-title">预防1</div>
                            <div class="form-row-d7">
                                <div class="form-group">
                                    <label for="d7_prevention1">预防措施</label>
                                    <textarea id="d7_prevention1" name="d7_prevention1" class="auto-resize-textarea"></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="d7_responsible1">责任人</label>
                                    <textarea id="d7_responsible1" name="d7_responsible1"></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="d7_deadline1">计划完成日期</label>
                                    <input type="date" id="d7_deadline1" name="d7_deadline1">
                                </div>
                            </div>
                        </div>
                        
                        <!-- 动态预防措施容器 -->
                        <div id="dynamic-d7-preventions-container"></div>
                        
                        <!-- 增加预防措施按钮 -->
                        <div class="add-member-section">
                            <button type="button" class="add-member-btn" onclick="addD7Prevention()">
                                <span>增加预防措施</span>
                            </button>
                            <div class="member-counter">
                                当前预防措施数：<span id="d7-prevention-count">1</span> 个
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- D8 庆贺团队 -->
                <div class="form-section" id="section-d8" data-section="d8">
                    <div class="section-header" onclick="toggleSection('d8')">
                        <div class="section-title-container">
                            <h2 class="section-title">D8 庆贺团队</h2>
                            <div class="section-desc">确认措施有效性并填写确认人及完成时间。</div>
                        </div>
                        <div class="section-controls">
                            <span class="completion-badge" id="completion-d8">0/0</span>
                            <button type="button" class="collapse-btn" id="collapse-btn-d8">
                                <span class="collapse-icon">−</span>
                            </button>
                        </div>
                    </div>
                    <div class="section-content">
                        <div class="form-row-d8">
                            <div class="form-group">
                                <label for="d8_effectiveness">有效性确认</label>
                                <textarea id="d8_effectiveness" name="d8_effectiveness" class="auto-resize-textarea"></textarea>
                            </div>
                            <div class="form-group">
                                <label for="d8_confirmer">确认人</label>
                                <textarea id="d8_confirmer" name="d8_confirmer"></textarea>
                            </div>
                            <div class="form-group">
                                <label for="d8_confirm_time">确认完成时间</label>
                                <input type="date" id="d8_confirm_time" name="d8_confirm_time">
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="submit-container">
                    <button type="button" class="action-btn ai-optimize-btn" id="ai-optimize-btn">
                        <span class="btn-icon">🤖</span>
                        <span class="btn-text">AI优化</span>
                        <span class="btn-loading" style="display: none;">
                            <span class="loading-spinner"></span>
                            AI优化中...
                        </span>
                    </button>
                    <button type="button" class="action-btn direct-generate-btn" id="direct-generate-btn">
                        <span class="btn-icon">📄</span>
                        <span class="btn-text">直接生成报告</span>
                        <span class="btn-loading" style="display: none;">
                            <span class="loading-spinner"></span>
                            生成中...
                        </span>
                    </button>
                </div>
            </form>
                </div>

                <!-- AI优化要求模态框 -->
                <div class="modal-overlay" id="ai-optimize-modal" style="display: none;">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3>AI优化要求</h3>
                            <button type="button" class="modal-close-btn" id="modal-close-btn">×</button>
                        </div>
                        <div class="modal-body">
                            <p>请描述您希望AI如何优化您的8D报告（可选）：</p>
                            <textarea 
                                id="optimize-requirements" 
                                placeholder="例如：请帮我完善问题描述，使其更详细具体；分析根本原因更深入；制定更有效的预防措施等..."
                                rows="4"
                                maxlength="500"></textarea>
                            <div class="char-count-container">
                                <span class="char-count"><span id="optimize-char-count">0</span>/500</span>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="modal-btn cancel-btn" id="modal-cancel-btn">取消</button>
                            <button type="button" class="modal-btn confirm-btn" id="modal-confirm-btn">开始AI优化</button>
                        </div>
                    </div>
                </div>


            </div>
        </div>
    </div>
</body>
</html> 




