/**
 * 对话式8D报告系统 - 界面样式
 * 包含标签栏、对话历史、版本管理等组件样式
 */

/* ==================== 头部样式 ==================== */
.header-content {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.header-left h1 {
    font-size: 2.2rem;
    margin-bottom: 5px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-left p {
    font-size: 1rem;
    opacity: 0.9;
    margin: 0;
}



.header-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    padding: 10px 16px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.header-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.header-btn .btn-icon {
    font-size: 16px;
}

.new-conversation-btn {
    background: rgba(40, 167, 69, 0.8) !important;
    border-color: rgba(40, 167, 69, 0.9) !important;
}

.new-conversation-btn:hover {
    background: rgba(40, 167, 69, 1) !important;
}

.conversation-history-btn {
    background: rgba(108, 117, 125, 0.8) !important;
    border-color: rgba(108, 117, 125, 0.9) !important;
}

.conversation-history-btn:hover {
    background: rgba(108, 117, 125, 1) !important;
}

/* ==================== 统一侧边栏样式 ==================== */
.unified-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: 280px;
    height: 100vh;
    background: #f8f9fa;
    border-right: 1px solid #e1e5e9;
    display: flex;
    flex-direction: column;
    z-index: 1000;
    transition: transform 0.3s ease;
    overflow: hidden;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

/* 侧边栏收纳状态 */
.unified-sidebar.collapsed {
    width: 60px;
    overflow: visible;
}

.unified-sidebar.collapsed .sidebar-header {
    padding: 8px;
    justify-content: center;
}

.unified-sidebar.collapsed .sidebar-toggle-btn {
    width: 44px;
    height: 44px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.unified-sidebar.collapsed .sidebar-toggle-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.unified-sidebar.collapsed .new-conversation-section {
    padding: 8px;
}

.unified-sidebar.collapsed .new-conversation-btn {
    width: 44px;
    height: 44px;
    border-radius: 12px;
    padding: 0;
    justify-content: center;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
}

.unified-sidebar.collapsed .new-conversation-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.unified-sidebar.collapsed .btn-text {
    display: none;
}

.unified-sidebar.collapsed .btn-icon {
    font-size: 20px;
}

/* 隐藏收纳状态下的其他内容 */
.unified-sidebar.collapsed .conversation-history-section,
.unified-sidebar.collapsed .version-history-section,
.unified-sidebar.collapsed .section-title {
    display: none;
}

/* 侧边栏收纳状态下的body样式 */
body.sidebar-collapsed {
    padding-left: 80px;
}

/* 悬浮的展开按钮 */
.floating-expand-btn {
    position: fixed;
    top: 20px;
    left: 20px;
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 12px;
    color: white;
    font-size: 20px;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
    z-index: 1001;
    display: none;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.floating-expand-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(102, 126, 234, 0.4);
}

body.sidebar-collapsed .floating-expand-btn {
    display: flex;
}

/* 侧边栏头部 */
.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 16px;
    background: white;
    border-bottom: 1px solid #e1e5e9;
    flex-shrink: 0;
}



.sidebar-toggle-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: #f8f9fa;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
    font-size: 18px;
    color: #495057;
}

.sidebar-toggle-btn:hover {
    background: #e9ecef;
    transform: scale(1.05);
}



/* 新建对话按钮 */
.new-conversation-section {
    padding: 16px;
    border-bottom: 1px solid #e1e5e9;
    flex-shrink: 0;
}

.new-conversation-btn {
    width: 100%;
    padding: 12px 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s;
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

.new-conversation-btn:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4c93 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.4);
}





/* 区域标题 */
.section-title {
    padding: 16px 16px 8px;
    font-size: 12px;
    font-weight: 600;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 对话历史区域 */
.conversation-history-section {
    height: 45%;
    overflow-y: auto;
    border-bottom: 1px solid #e1e5e9;
    flex-shrink: 0;
}

.conversation-list {
    padding: 0 8px 16px;
}

.conversation-item {
    padding: 8px 12px;
    margin-bottom: 2px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
    position: relative;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 13px;
}

.conversation-item:hover {
    background: #e9ecef;
    transform: translateX(2px);
}

.conversation-item.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

.conversation-icon {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    background: #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    flex-shrink: 0;
}

.conversation-item.active .conversation-icon {
    background: rgba(255, 255, 255, 0.2);
}

.conversation-title {
    font-size: 13px;
    font-weight: 500;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 1.3;
}

.conversation-delete-btn {
    width: 18px;
    height: 18px;
    border: none;
    background: none;
    color: #6c757d;
    cursor: pointer;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.2s;
    font-size: 12px;
}

.conversation-item:hover .conversation-delete-btn {
    opacity: 1;
}

.conversation-delete-btn:hover {
    background: #dc3545;
    color: white;
}

.conversation-item.active .conversation-delete-btn {
    color: rgba(255, 255, 255, 0.7);
}

.conversation-item.active .conversation-delete-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

/* 版本历史区域 */
.version-history-section {
    height: 55%;
    overflow-y: auto;
    flex-shrink: 0;
}

.version-list {
    padding: 0 8px 16px;
}

.version-item {
    padding: 8px 12px;
    margin-bottom: 2px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
}

.version-item:hover {
    background: #e9ecef;
    transform: translateX(2px);
}

.version-item.active {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.version-icon {
    width: 18px;
    height: 18px;
    border-radius: 3px;
    background: #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 9px;
    flex-shrink: 0;
}

.version-item.active .version-icon {
    background: rgba(255, 255, 255, 0.2);
}

.version-info {
    flex: 1;
    overflow: hidden;
}

.version-name {
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 1.3;
}

.version-meta {
    font-size: 10px;
    opacity: 0.7;
    margin-top: 1px;
}

.version-delete-btn {
    width: 16px;
    height: 16px;
    border: none;
    background: none;
    color: #6c757d;
    cursor: pointer;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.2s;
    font-size: 10px;
}

.version-item:hover .version-delete-btn {
    opacity: 1;
}

.version-delete-btn:hover {
    background: #dc3545;
    color: white;
}

.version-item.active .version-delete-btn {
    color: rgba(255, 255, 255, 0.7);
}

.version-item.active .version-delete-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

/* 空状态 */
.no-conversations {
    padding: 20px 12px;
    text-align: center;
    color: #6c757d;
    font-size: 13px;
    font-style: italic;
}

/* 滚动条样式 */
.conversation-history-section::-webkit-scrollbar,
.version-history-section::-webkit-scrollbar {
    width: 4px;
}

.conversation-history-section::-webkit-scrollbar-track,
.version-history-section::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
}

.conversation-history-section::-webkit-scrollbar-thumb,
.version-history-section::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
}

.conversation-history-section::-webkit-scrollbar-thumb:hover,
.version-history-section::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 侧边栏遮罩层 */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    display: none;
}

/* 主内容区域调整 */
body {
    padding-left: 280px;
    transition: padding-left 0.3s ease;
}

body.sidebar-collapsed {
    padding-left: 0;
}

/* ==================== 响应式设计 ==================== */
/* 中等屏幕自动收起 */
@media (max-width: 1200px) {
    .unified-sidebar {
        transform: translateX(-280px);
    }

    .unified-sidebar.mobile-open {
        transform: translateX(0);
    }

    .sidebar-overlay.show {
        display: block;
    }

    body {
        padding-left: 0;
    }

    /* 显示悬浮的侧边栏切换按钮 */
    .floating-sidebar-toggle {
        position: fixed;
        top: 20px;
        left: 20px;
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 8px;
        color: white;
        cursor: pointer;
        display: none; /* 默认隐藏，通过JavaScript控制显示 */
        align-items: center;
        justify-content: center;
        z-index: 1001;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
        font-size: 18px;
    }

    .floating-sidebar-toggle:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
    }

    .floating-sidebar-toggle:active {
        transform: scale(0.95);
    }
}

@media (max-width: 768px) {
    .unified-sidebar {
        width: 100%;
        max-width: 320px;
    }

    .floating-sidebar-toggle {
        width: 36px;
        height: 36px;
        top: 16px;
        left: 16px;
        font-size: 16px;
    }

    /* 移动端右侧导航调整 */
    .right-navigation {
        right: 10px;
        top: 60%;
    }

    .right-navigation.expanded {
        width: 160px;
    }
}

/* ==================== 动画效果 ==================== */
@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.version-tabs-container {
    animation: slideInFromTop 0.3s ease-out;
}

.conversation-history-panel {
    animation: slideInFromRight 0.3s ease-out;
}

.version-tab {
    animation: slideInFromTop 0.2s ease-out;
}

/* ==================== 工具提示样式 ==================== */
[title]:hover::before {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 9999;
    margin-bottom: 4px;
}

/* ==================== 状态指示器 ==================== */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
}

.status-indicator.active {
    background: #28a745;
    animation: pulse 2s infinite;
}

.status-indicator.inactive {
    background: #6c757d;
}

.status-indicator.error {
    background: #dc3545;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

/* ==================== 右侧章节导航 ==================== */
.right-navigation {
    position: fixed;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    background: white;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    z-index: 1000;
    overflow: visible;
    transition: all 0.3s ease;
    width: 70px;
    padding: 12px 0;
}

.nav-sections {
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.nav-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px 6px;
    cursor: pointer;
    transition: all 0.2s;
    margin: 2px 8px;
    border-radius: 10px;
    position: relative;
}

.nav-section:hover {
    background: #f0f4ff;
    transform: translateY(-1px);
}

.nav-section.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.section-label {
    font-size: 12px;
    font-weight: 600;
    color: #667eea;
    text-align: center;
    margin-bottom: 2px;
}

.nav-section.active .section-label {
    color: white;
}

.section-progress {
    font-size: 9px;
    color: #6c757d;
    font-weight: 500;
    background: rgba(255, 255, 255, 0.9);
    padding: 1px 4px;
    border-radius: 6px;
    min-width: 16px;
    text-align: center;
}

.nav-section.active .section-progress {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

/* 悬停时显示章节名称 */
.section-tooltip {
    position: absolute;
    right: 100%;
    top: 50%;
    transform: translateY(-50%);
    background: #333;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s;
    margin-right: 8px;
    z-index: 1001;
}

.section-tooltip::after {
    content: '';
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    border: 5px solid transparent;
    border-left-color: #333;
}

.nav-section:hover .section-tooltip {
    opacity: 1;
    visibility: visible;
}





/* ==================== 自定义确认对话框 ==================== */
.custom-confirm-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    animation: fadeIn 0.2s ease;
}

.custom-confirm-content {
    background: white;
    border-radius: 16px;
    padding: 32px;
    max-width: 420px;
    width: 90%;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    animation: slideIn 0.3s ease;
    position: relative;
}

.custom-confirm-close {
    position: absolute;
    top: 16px;
    right: 16px;
    background: none;
    border: none;
    font-size: 20px;
    color: #999;
    cursor: pointer;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s;
}

.custom-confirm-close:hover {
    background: #f5f5f5;
    color: #666;
}

.custom-confirm-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
}

.custom-confirm-icon {
    width: 40px;
    height: 40px;
    background: #ff6b35;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
    flex-shrink: 0;
}

.custom-confirm-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.custom-confirm-message {
    color: #666;
    margin-bottom: 32px;
    line-height: 1.5;
    font-size: 14px;
}

.custom-confirm-buttons {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.custom-confirm-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    min-width: 80px;
}

.cancel-btn {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #e9ecef;
}

.cancel-btn:hover {
    background: #e9ecef;
}

.confirm-btn {
    background: #ff6b35;
    color: white;
}

.confirm-btn:hover {
    background: #e55a2b;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}
