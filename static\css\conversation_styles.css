/**
 * 对话式8D报告系统 - 界面样式
 * 包含标签栏、对话历史、版本管理等组件样式
 */

/* ==================== 头部样式 ==================== */
.header-content {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.header-left h1 {
    font-size: 2.2rem;
    margin-bottom: 5px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-left p {
    font-size: 1rem;
    opacity: 0.9;
    margin: 0;
}



.header-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    padding: 10px 16px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.header-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.header-btn .btn-icon {
    font-size: 16px;
}

.new-conversation-btn {
    background: rgba(40, 167, 69, 0.8) !important;
    border-color: rgba(40, 167, 69, 0.9) !important;
}

.new-conversation-btn:hover {
    background: rgba(40, 167, 69, 1) !important;
}

.conversation-history-btn {
    background: rgba(108, 117, 125, 0.8) !important;
    border-color: rgba(108, 117, 125, 0.9) !important;
}

.conversation-history-btn:hover {
    background: rgba(108, 117, 125, 1) !important;
}

/* ==================== 统一侧边栏样式 ==================== */
.unified-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: 280px;
    height: 100vh;
    background: #f8f9fa;
    border-right: 1px solid #e1e5e9;
    display: flex;
    flex-direction: column;
    z-index: 1000;
    transition: transform 0.3s ease;
    overflow: hidden;
}

.unified-sidebar.collapsed {
    transform: translateX(-280px);
}

/* 侧边栏头部 */
.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    background: white;
    border-bottom: 1px solid #e1e5e9;
    flex-shrink: 0;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #667eea;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: white;
}

.user-name {
    font-weight: 500;
    color: #333;
}

.sidebar-toggle-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: #f8f9fa;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s;
}

.sidebar-toggle-btn:hover {
    background: #e9ecef;
}

/* 新建对话按钮 */
.new-conversation-section {
    padding: 16px;
    border-bottom: 1px solid #e1e5e9;
    flex-shrink: 0;
}

.new-conversation-btn {
    width: 100%;
    padding: 12px 16px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    transition: background 0.2s;
}

.new-conversation-btn:hover {
    background: #5a6fd8;
}

.btn-shortcut {
    margin-left: auto;
    font-size: 12px;
    opacity: 0.7;
}

/* 区域标题 */
.section-title {
    padding: 16px 16px 8px;
    font-size: 12px;
    font-weight: 600;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 对话历史区域 */
.conversation-history-section {
    flex: 1;
    overflow-y: auto;
    border-bottom: 1px solid #e1e5e9;
}

.conversation-list {
    padding: 0 8px 16px;
}

.conversation-item {
    padding: 12px;
    margin-bottom: 4px;
    border-radius: 8px;
    cursor: pointer;
    transition: background 0.2s;
    position: relative;
    display: flex;
    align-items: center;
    gap: 12px;
}

.conversation-item:hover {
    background: #e9ecef;
}

.conversation-item.active {
    background: #667eea;
    color: white;
}

.conversation-icon {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    background: #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    flex-shrink: 0;
}

.conversation-item.active .conversation-icon {
    background: rgba(255, 255, 255, 0.2);
}

.conversation-title {
    font-size: 14px;
    font-weight: 500;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.conversation-delete-btn {
    width: 20px;
    height: 20px;
    border: none;
    background: none;
    color: #6c757d;
    cursor: pointer;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.2s;
    font-size: 12px;
}

.conversation-item:hover .conversation-delete-btn {
    opacity: 1;
}

.conversation-delete-btn:hover {
    background: #dc3545;
    color: white;
}

.conversation-item.active .conversation-delete-btn {
    color: rgba(255, 255, 255, 0.7);
}

.conversation-item.active .conversation-delete-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

/* 版本历史区域 */
.version-history-section {
    max-height: 40%;
    overflow-y: auto;
    flex-shrink: 0;
}

.version-list {
    padding: 0 8px 16px;
}

.version-item {
    padding: 10px 12px;
    margin-bottom: 4px;
    border-radius: 6px;
    cursor: pointer;
    transition: background 0.2s;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 13px;
}

.version-item:hover {
    background: #e9ecef;
}

.version-item.active {
    background: #28a745;
    color: white;
}

.version-icon {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    background: #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    flex-shrink: 0;
}

.version-item.active .version-icon {
    background: rgba(255, 255, 255, 0.2);
}

.version-info {
    flex: 1;
    overflow: hidden;
}

.version-name {
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.version-meta {
    font-size: 11px;
    opacity: 0.7;
    margin-top: 2px;
}

.version-delete-btn {
    width: 18px;
    height: 18px;
    border: none;
    background: none;
    color: #6c757d;
    cursor: pointer;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.2s;
    font-size: 10px;
}

.version-item:hover .version-delete-btn {
    opacity: 1;
}

.version-delete-btn:hover {
    background: #dc3545;
    color: white;
}

.version-item.active .version-delete-btn {
    color: rgba(255, 255, 255, 0.7);
}

.version-item.active .version-delete-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

/* 空状态 */
.no-conversations {
    padding: 20px 12px;
    text-align: center;
    color: #6c757d;
    font-size: 13px;
}

/* 侧边栏遮罩层 */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    display: none;
}

/* 主内容区域调整 */
body {
    padding-left: 280px;
    transition: padding-left 0.3s ease;
}

body.sidebar-collapsed {
    padding-left: 0;
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 1024px) {
    .unified-sidebar {
        transform: translateX(-280px);
    }
    
    .unified-sidebar.mobile-open {
        transform: translateX(0);
    }
    
    .sidebar-overlay.show {
        display: block;
    }
    
    body {
        padding-left: 0;
    }
}

@media (max-width: 768px) {
    .unified-sidebar {
        width: 100%;
        max-width: 320px;
    }
}

/* ==================== 动画效果 ==================== */
@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.version-tabs-container {
    animation: slideInFromTop 0.3s ease-out;
}

.conversation-history-panel {
    animation: slideInFromRight 0.3s ease-out;
}

.version-tab {
    animation: slideInFromTop 0.2s ease-out;
}

/* ==================== 工具提示样式 ==================== */
[title]:hover::before {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 9999;
    margin-bottom: 4px;
}

/* ==================== 状态指示器 ==================== */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
}

.status-indicator.active {
    background: #28a745;
    animation: pulse 2s infinite;
}

.status-indicator.inactive {
    background: #6c757d;
}

.status-indicator.error {
    background: #dc3545;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
} 
