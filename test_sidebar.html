<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>侧边栏测试页面</title>
    <style>
        /* 简化的测试样式 */
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding-left: 280px;
            transition: padding-left 0.3s ease;
        }
        
        body.sidebar-collapsed {
            padding-left: 0;
        }
        
        .main-content {
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .test-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #5a6fd8;
        }
    </style>
    <link rel="stylesheet" href="static/css/conversation_styles.css">
</head>
<body>
    <!-- 优化后的统一侧边栏 -->
    <div class="unified-sidebar" id="unified-sidebar">
        <!-- 侧边栏头部 -->
        <div class="sidebar-header">
            <div class="user-info">
                <div class="user-avatar">👤</div>
                <div class="user-name">豆包</div>
            </div>
            <button class="sidebar-toggle-btn" id="sidebar-toggle-btn" onclick="toggleSidebar()">
                <span class="toggle-icon">📋</span>
            </button>
        </div>
        
        <!-- 新建对话按钮 -->
        <div class="new-conversation-section">
            <button class="new-conversation-btn" onclick="createNewConversation()">
                <span class="btn-icon">+</span>
                <span class="btn-text">新对话</span>
                <span class="btn-shortcut">Ctrl+N</span>
            </button>
        </div>
        
        <!-- 功能导航区域 -->
        <div class="sidebar-navigation">
            <div class="nav-item" onclick="navigateToAISearch()">
                <span class="nav-icon">🔍</span>
                <span class="nav-text">AI 搜索</span>
            </div>
            <div class="nav-item" onclick="navigateToHelp()">
                <span class="nav-icon">✏️</span>
                <span class="nav-text">帮我写作</span>
            </div>
            <div class="nav-item" onclick="navigateToCode()">
                <span class="nav-icon">💻</span>
                <span class="nav-text">AI 编程</span>
            </div>
            <div class="nav-item" onclick="navigateToImage()">
                <span class="nav-icon">🎨</span>
                <span class="nav-text">图像生成</span>
            </div>
            <div class="nav-item nav-more" onclick="toggleMoreOptions()">
                <span class="nav-icon">📋</span>
                <span class="nav-text">更多</span>
                <span class="nav-arrow">›</span>
            </div>
        </div>
        
        <!-- 对话历史列表 -->
        <div class="conversation-history-section">
            <div class="section-title">历史对话</div>
            <div class="conversation-list" id="conversation-list">
                <!-- 示例对话项 -->
                <div class="conversation-item active">
                    <div class="conversation-icon">💬</div>
                    <div class="conversation-title">关于8D报告的5W2H分析</div>
                    <button class="conversation-delete-btn">×</button>
                </div>
                <div class="conversation-item">
                    <div class="conversation-icon">💬</div>
                    <div class="conversation-title">解释质量管理体系</div>
                    <button class="conversation-delete-btn">×</button>
                </div>
                <div class="conversation-item">
                    <div class="conversation-icon">💬</div>
                    <div class="conversation-title">Python代码错误分析</div>
                    <button class="conversation-delete-btn">×</button>
                </div>
                <div class="conversation-item">
                    <div class="conversation-icon">💬</div>
                    <div class="conversation-title">Navicat功能介绍</div>
                    <button class="conversation-delete-btn">×</button>
                </div>
            </div>
        </div>
        
        <!-- 当前对话的版本列表 -->
        <div class="version-history-section" id="version-history-section">
            <div class="section-title">版本记录</div>
            <div class="version-list" id="version-list">
                <!-- 示例版本项 -->
                <div class="version-item active">
                    <div class="version-icon">🤖</div>
                    <div class="version-info">
                        <div class="version-name">AI增强版本1</div>
                        <div class="version-meta">07-17 14:30</div>
                    </div>
                    <button class="version-delete-btn">×</button>
                </div>
                <div class="version-item">
                    <div class="version-icon">👤</div>
                    <div class="version-info">
                        <div class="version-name">原始输入</div>
                        <div class="version-meta">07-17 14:25</div>
                    </div>
                    <button class="version-delete-btn">×</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 侧边栏遮罩层（移动端） -->
    <div class="sidebar-overlay" id="sidebar-overlay" onclick="closeSidebar()"></div>

    <!-- 悬浮的侧边栏切换按钮（中等屏幕显示） -->
    <button class="floating-sidebar-toggle" id="floating-sidebar-toggle" onclick="toggleSidebar()" style="display: none;">
        📋
    </button>

    <!-- 主内容区域 -->
    <div class="main-content">
        <div class="test-card">
            <h2>侧边栏优化测试</h2>
            <p>这是一个测试页面，用于验证侧边栏的优化效果。</p>
            <p>主要改进包括：</p>
            <ul>
                <li>✅ 合并历史对话记录和版本记录到统一侧边栏</li>
                <li>✅ 简化对话记录显示，去除冗余信息</li>
                <li>✅ 点击即可加载，无需额外按钮</li>
                <li>✅ 实现侧边栏收纳功能</li>
                <li>✅ 响应式设计，自动适应屏幕大小</li>
                <li>✅ 类似豆包的现代化风格</li>
            </ul>
        </div>
        
        <div class="test-card">
            <h3>测试功能</h3>
            <button class="test-button" onclick="toggleSidebar()">切换侧边栏</button>
            <button class="test-button" onclick="testResize()">模拟屏幕缩放</button>
            <button class="test-button" onclick="addTestConversation()">添加测试对话</button>
        </div>
        
        <div class="test-card">
            <h3>响应式测试</h3>
            <p>请尝试调整浏览器窗口大小：</p>
            <ul>
                <li>大屏幕（>1200px）：侧边栏始终显示</li>
                <li>中等屏幕（≤1200px）：侧边栏自动收起，显示悬浮按钮</li>
                <li>小屏幕（≤768px）：侧边栏全屏显示，带遮罩层</li>
            </ul>
        </div>
    </div>

    <script>
        // 简化的测试脚本
        function toggleSidebar() {
            const sidebar = document.getElementById('unified-sidebar');
            const overlay = document.getElementById('sidebar-overlay');
            
            if (sidebar.classList.contains('mobile-open')) {
                sidebar.classList.remove('mobile-open');
                overlay.classList.remove('show');
            } else {
                sidebar.classList.add('mobile-open');
                overlay.classList.add('show');
            }
        }
        
        function closeSidebar() {
            const sidebar = document.getElementById('unified-sidebar');
            const overlay = document.getElementById('sidebar-overlay');
            sidebar.classList.remove('mobile-open');
            overlay.classList.remove('show');
        }
        
        function createNewConversation() {
            alert('创建新对话功能');
        }
        
        function navigateToAISearch() { alert('AI搜索功能'); }
        function navigateToHelp() { alert('帮我写作功能'); }
        function navigateToCode() { alert('AI编程功能'); }
        function navigateToImage() { alert('图像生成功能'); }
        function toggleMoreOptions() { alert('更多选项功能'); }
        
        function testResize() {
            // 模拟屏幕大小变化
            const body = document.body;
            const sidebar = document.getElementById('unified-sidebar');
            const floatingToggle = document.getElementById('floating-sidebar-toggle');
            
            if (window.innerWidth > 1200) {
                // 模拟中等屏幕
                body.style.paddingLeft = '0';
                sidebar.classList.add('collapsed');
                floatingToggle.style.display = 'flex';
                alert('模拟中等屏幕：侧边栏收起，显示悬浮按钮');
            } else {
                // 模拟大屏幕
                body.style.paddingLeft = '280px';
                sidebar.classList.remove('collapsed');
                floatingToggle.style.display = 'none';
                alert('模拟大屏幕：侧边栏展开，隐藏悬浮按钮');
            }
        }
        
        function addTestConversation() {
            const list = document.getElementById('conversation-list');
            const item = document.createElement('div');
            item.className = 'conversation-item';
            item.innerHTML = `
                <div class="conversation-icon">💬</div>
                <div class="conversation-title">新测试对话 ${Date.now()}</div>
                <button class="conversation-delete-btn">×</button>
            `;
            list.appendChild(item);
        }
        
        // 初始化响应式功能
        function initResponsive() {
            const handleResize = () => {
                const floatingToggle = document.getElementById('floating-sidebar-toggle');
                const sidebar = document.getElementById('unified-sidebar');
                const body = document.body;
                
                if (window.innerWidth <= 1200) {
                    floatingToggle.style.display = 'flex';
                    sidebar.classList.add('collapsed');
                    body.classList.add('sidebar-collapsed');
                } else {
                    floatingToggle.style.display = 'none';
                    sidebar.classList.remove('collapsed', 'mobile-open');
                    body.classList.remove('sidebar-collapsed');
                    closeSidebar();
                }
            };
            
            handleResize();
            window.addEventListener('resize', handleResize);
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initResponsive);
    </script>
</body>
</html>
